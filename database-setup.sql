-- 工业地理开发平台数据库设置脚本
-- Industrial Geography Development Platform Database Setup Script

-- 创建数据库
CREATE DATABASE IF NOT EXISTS platform_core_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE platform_core_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL UNIQUE,
    preferred_language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    active_subscription_status VARCHAR(50) DEFAULT 'free',
    INDEX idx_email (email),
    INDEX idx_username (username)
);

-- 创建项目表
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    description TEXT,
    natural_language_input TEXT,
    structured_parameters TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 插入示例用户（可选）
INSERT IGNORE INTO users (email, password_hash, username, preferred_language) VALUES 
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'demo_user', 'en'),
('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'test_user', 'zh');

-- 插入示例项目（可选）
INSERT IGNORE INTO projects (user_id, project_name, description, natural_language_input, status) VALUES 
(1, '示例工业园区分析', '这是一个示例项目，用于演示AI选址分析功能', '分析位于长三角地区的高科技工业园区选址', 'active'),
(1, 'Sample Industrial Park Analysis', 'This is a sample project to demonstrate AI site analysis features', 'Analyze high-tech industrial park site selection in the Yangtze River Delta region', 'active');

-- 显示创建的表
SHOW TABLES;

-- 显示用户表结构
DESCRIBE users;

-- 显示项目表结构
DESCRIBE projects;

-- 显示插入的数据
SELECT 'Users:' as table_name;
SELECT id, email, username, preferred_language, created_at FROM users;

SELECT 'Projects:' as table_name;
SELECT id, user_id, project_name, description, status, created_at FROM projects;

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { FiGlobe, FiSun, FiMoon, FiMap, FiFilter, FiSliders, FiX, FiMaximize2, FiMinimize2, FiCheckSquare, FiSquare, FiMapPin, FiTrash2, FiGrid, FiNavigation, FiInfo, FiSearch, FiSettings, FiHome } from 'react-icons/fi';
import { IoOptionsOutline, IoFunnelOutline, IoLayersOutline, IoEarthOutline, IoAnalyticsOutline, IoFlameOutline } from 'react-icons/io5';
import { BiWorld, BiAnchor, BiBuildings, BiCurrentLocation } from 'react-icons/bi';
import { motion, AnimatePresence } from 'framer-motion';
import '../../styles/MapButtons.css';
import { useNavigate } from 'react-router-dom';

// 弹出菜单组件
const MapPopover = ({ children, trigger, position = 'left', isDarkMode, isMarkerMenu = false, isMarkerModeActive = false, tooltipText }) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef(null);

  // 点击外部关闭弹出菜单 - 但标记菜单在标记模式激活时除外，此时标记菜单只能通过再次点击按钮关闭
  useEffect(() => {
    function handleClickOutside(event) {
      // 如果是标记菜单且标记模式已激活，不通过点击外部关闭
      if (isMarkerMenu && isMarkerModeActive) {
        return;
      }

      // 检查点击的元素是否在图层说明菜单内
      const isLayerExplanation = event.target.closest('.layer-explanation');

      // 检查点击的元素是否是图层说明菜单的关闭按钮
      const isLayerExplanationCloseButton =
        event.target.closest('.layer-explanation-close-button') ||
        event.target.hasAttribute('data-layer-explanation-close') ||
        event.target.parentElement?.hasAttribute('data-layer-explanation-close');

      // 如果点击的是图层说明菜单内的元素或关闭按钮，不关闭弹出菜单
      if (isLayerExplanation || isLayerExplanationCloseButton) {
        return;
      }

      // 如果点击的不是弹出菜单内的元素，关闭弹出菜单
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [popoverRef, isMarkerMenu, isMarkerModeActive]);

  return (
    <div className={`map-popover ${position}`} ref={popoverRef}>
      <button
        className={`map-button circle ${isOpen ? 'active' : ''} ${isDarkMode ? 'dark-mode' : ''}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="map-button-icon">{trigger}</span>
      </button>
      {/* 悬停提示框 - 仅在菜单关闭时显示 */}
      {tooltipText && !isOpen && (
        <div className={`map-button-tooltip ${isDarkMode ? 'dark-mode' : ''}`}>
          {tooltipText}
        </div>
      )}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className={`map-popover-content ${isDarkMode ? 'dark-mode' : ''}`}
            initial={{ opacity: 0, x: position === 'left' ? -10 : 10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: position === 'left' ? -10 : 10 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

const EnhancedMapControls = ({
  // 地图样式属性
  mapStyle,
  onStyleChange,
  displayMode,
  onDisplayModeChange,
  t,
  language,
  toggleLanguage,

  // 国家选择
  selectedCountry,
  handleCountryChange,

  // 自定义标记模式
  isCustomMarkerMode,
  toggleCustomMarkerMode,
  clearAllCustomMarkers,

  // 图层控制
  visibleLayers = [],
  toggleLayer,

  // 经济热点图层
  isEconomicHotspotActive = false,
  toggleEconomicHotspot,
  economicHotspotSettings = {},
  updateEconomicHotspotSettings,

  // 美国过滤器
  leaseMaxRange,
  minLease,
  setMinLease,
  maxLease,
  setMaxLease,
  stateOptions,
  selectedStates,
  setSelectedStates,
  handleSelectAllStates,
  handleClearStates,
  stateFullNames,
  cityOptions,
  filteredCityOptions,
  selectedCities,
  setSelectedCities,
  citySearchTerm,
  setCitySearchTerm,
  showAllCities,
  setShowAllCities,

  // 泰国过滤器
  handleFocusThaiProvince,
  THAILAND_PROVINCE_COORDINATES,
  COUNTRY_COORDINATES,

  // 园区数据和操作
  filteredData,
  filteredThaiData,
  handleFocusPark
}) => {
  const navigate = useNavigate();
  const [isDarkMode, setIsDarkMode] = useState(mapStyle === 'night');
  const [showFilters, setShowFilters] = useState(false);
  const [showSearch, setShowSearch] = useState(false); // 新增：控制搜索框显示状态
  const [searchTerm, setSearchTerm] = useState(''); // 新增：搜索词状态
  const [searchResults, setSearchResults] = useState([]); // 新增：搜索结果状态
  const [isSearching, setIsSearching] = useState(false); // 新增：搜索中状态
  const [hasSearched, setHasSearched] = useState(false); // 新增：是否已搜索状态
  const [stateListExpanded, setStateListExpanded] = useState(false);
  const [cityListExpanded, setCityListExpanded] = useState(false);
  const [unitsExpanded, setUnitsExpanded] = useState(false);
  const [currencyExpanded, setCurrencyExpanded] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState('metric'); // 默认公制
  const [selectedCurrency, setSelectedCurrency] = useState('USD'); // 默认美元

  // 创建按钮引用，用于定位面板
  const filterButtonRef = useRef(null);
  const searchButtonRef = useRef(null);

  useEffect(() => {
    setIsDarkMode(mapStyle === 'night');
  }, [mapStyle]);

  // 图层选项
  const layerOptions = [
    { id: 'grid', name: t?.gridLayer || '网格图层', icon: <FiGrid /> },
    { id: 'traffic', name: t?.trafficLayer || '交通图层', icon: <FiNavigation /> },
    { id: 'poi', name: t?.poiLayer || '兴趣点', icon: <FiInfo /> },
    { id: 'terrain', name: t?.terrainLayer || '地形图层', icon: <IoEarthOutline /> },
    { id: 'analytics', name: t?.analyticsLayer || '分析图层', icon: <IoAnalyticsOutline /> }
  ];

  const mapStyles = [
    { id: 'day', name: t?.dayStyle || '日间模式', icon: <FiSun /> },
    { id: 'night', name: t?.nightStyle || '夜间模式', icon: <FiMoon /> },
    { id: 'satellite', name: t?.satelliteStyle || '卫星图', icon: <FiMap /> }
  ];

  const displayModes = [
    { id: 'all', name: t?.allLocations || '全部', icon: <BiWorld /> },
    { id: 'ports', name: t?.portsOnly || '港口', icon: <BiAnchor /> },
    { id: 'cities', name: t?.citiesOnly || '城市', icon: <BiBuildings /> }
  ];

  const currentStyle = mapStyles.find(s => s.id === mapStyle) || mapStyles[0];
  const currentMode = displayModes.find(m => m.id === displayMode) || displayModes[0];

  const handleLeaseInputChange = (value, type) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (type === 'min') {
      setMinLease(numericValue);
    } else if (type === 'max') {
      setMaxLease(numericValue);
    }
  };

  // 计算激活的过滤器数量
  const activeFiltersCount = (
    selectedStates.length +
    selectedCities.length +
    ((minLease && minLease !== '0') || (maxLease && maxLease !== '0') ? 1 : 0)
  );

  // 模糊搜索函数
  const fuzzySearch = (term, items, getNameFunc) => {
    if (!term || term.trim() === '') return [];

    const normalizedTerm = term.toLowerCase().trim();

    // 精确匹配
    const exactMatches = items.filter(item => {
      const name = getNameFunc(item).toLowerCase();
      return name === normalizedTerm;
    });

    // 开头匹配
    const startsWithMatches = items.filter(item => {
      const name = getNameFunc(item).toLowerCase();
      return name.startsWith(normalizedTerm) && !exactMatches.includes(item);
    });

    // 包含匹配
    const containsMatches = items.filter(item => {
      const name = getNameFunc(item).toLowerCase();
      return name.includes(normalizedTerm) &&
             !exactMatches.includes(item) &&
             !startsWithMatches.includes(item);
    });

    // 合并结果，优先级：精确匹配 > 开头匹配 > 包含匹配
    return [...exactMatches, ...startsWithMatches, ...containsMatches];
  };

  // 执行搜索
  const performSearch = () => {
    setIsSearching(true);
    setHasSearched(true);

    try {
      let results = [];

      if (selectedCountry === 'USA') {
        // 搜索美国园区
        const parkNameResults = fuzzySearch(searchTerm, filteredData, item => item.park || '');

        // 检查是否匹配州名或州缩写
        const normalizedSearchTerm = searchTerm.toLowerCase().trim();

        // 查找匹配的州（全名或缩写）
        let matchedStates = [];

        // 检查州缩写匹配
        Object.keys(stateFullNames).forEach(stateAbbr => {
          if (stateAbbr.toLowerCase() === normalizedSearchTerm) {
            matchedStates.push(stateAbbr);
          }
        });

        // 检查州全名匹配
        Object.entries(stateFullNames).forEach(([stateAbbr, stateName]) => {
          if (stateName.toLowerCase() === normalizedSearchTerm) {
            matchedStates.push(stateAbbr);
          } else if (stateName.toLowerCase().includes(normalizedSearchTerm)) {
            // 部分匹配州名
            matchedStates.push(stateAbbr);
          }
        });

        // 如果找到匹配的州，添加该州的所有园区
        if (matchedStates.length > 0) {
          const stateResults = filteredData.filter(item =>
            matchedStates.includes(item.state) &&
            !parkNameResults.includes(item) // 避免重复
          );

          // 合并结果，优先显示园区名称匹配的结果
          results = [...parkNameResults, ...stateResults];
        } else {
          results = parkNameResults;
        }
      } else if (selectedCountry === 'THAILAND') {
        // 搜索泰国园区
        results = fuzzySearch(searchTerm, filteredThaiData, item => item.name || '');
      }

      setSearchResults(results);
    } catch (error) {
      console.error('搜索出错:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理搜索结果点击
  const handleSearchResultClick = (parkItem) => {
    if (handleFocusPark) {
      handleFocusPark(parkItem);
    }
    setShowSearch(false); // 关闭搜索面板
  };

  // 返回首页
  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div className={`enhanced-map-controls ${isDarkMode ? 'dark-mode' : ''}`}>
      {/* 左侧控制栏 */}
      <div className="left-controls">
        {/* 首页按钮 */}
        <button
          className="control-button home-button"
          onClick={handleGoHome}
          title={language === 'en' ? 'Return to Home' : '返回首页'}
        >
          <FiHome />
        </button>

        {/* 地图样式切换 */}
        <MapPopover
          trigger={currentStyle.icon}
          position="left"
          isDarkMode={isDarkMode}
          tooltipText={t?.mapStyleSwitch || "切换地图样式"}
        >
          {mapStyles.map(style => (
            <button
              key={style.id}
              className={`map-popover-item ${mapStyle === style.id ? 'active' : ''}`}
              onClick={() => onStyleChange(style.id)}
            >
              {style.icon}
              <span>{style.name}</span>
            </button>
          ))}
        </MapPopover>

        {/* 显示模式选择器 */}
        <MapPopover
          trigger={currentMode.icon}
          position="left"
          isDarkMode={isDarkMode}
          tooltipText={t?.displayModeSwitch || "切换显示模式"}
        >
          {displayModes.map(mode => (
            <button
              key={mode.id}
              className={`map-popover-item ${displayMode === mode.id ? 'active' : ''}`}
              onClick={() => onDisplayModeChange(mode.id)}
            >
              {mode.icon}
              <span>{mode.name}</span>
            </button>
          ))}
        </MapPopover>

        {/* 自定义标记模式按钮 */}
        <MapPopover
          trigger={<FiMapPin />}
          position="left"
          isDarkMode={isDarkMode}
          isMarkerMenu={true}
          isMarkerModeActive={isCustomMarkerMode}
          tooltipText={t?.customMarkers || "自定义标记"}
        >
          <button
            className={`map-popover-item ${isCustomMarkerMode ? 'active' : ''}`}
            onClick={toggleCustomMarkerMode}
          >
            <FiMapPin />
            <span>{isCustomMarkerMode ? (t?.stopPlacingMarkers || '停止放置标记') : (t?.placeMarkers || '放置标记')}</span>
          </button>
          <button
            className="map-popover-item"
            onClick={clearAllCustomMarkers}
          >
            <FiTrash2 />
            <span>{t?.clearAllMarkers || '清除所有标记'}</span>
          </button>
        </MapPopover>

        {/* 图层控制按钮 */}
        <MapPopover
          trigger={<IoLayersOutline />}
          position="left"
          isDarkMode={isDarkMode}
          tooltipText={t?.layerControl || "图层控制"}
        >
          {layerOptions.map(layer => (
            <button
              key={layer.id}
              className={`map-popover-item ${visibleLayers.includes(layer.id) ? 'active' : ''}`}
              onClick={() => toggleLayer(layer.id)}
            >
              {layer.icon}
              <span>{layer.name}</span>
            </button>
          ))}
        </MapPopover>

        {/* 语言切换按钮 */}
        <MapPopover
          trigger={<FiGlobe />}
          position="left"
          isDarkMode={isDarkMode}
          tooltipText={t?.languageOptions || "语言选项"}
        >
          <button
            className={`map-popover-item ${language === 'en' ? 'active' : ''}`}
            onClick={() => {
              if (language !== 'en') toggleLanguage();
            }}
          >
            <span>{t?.english || "English"}</span>
          </button>
          <button
            className={`map-popover-item ${language === 'zh' ? 'active' : ''}`}
            onClick={() => {
              if (language !== 'zh') toggleLanguage();
            }}
          >
            <span>{t?.chinese || "中文"}</span>
          </button>
          {/* 为未来更多语言预留的空间 */}
        </MapPopover>

        {/* 经济热点按钮 - 确保hover效果正常工作 */}
        <div className="map-popover">
          <button
            className={`map-button circle hover-enabled ${isEconomicHotspotActive ? 'active' : ''} ${isDarkMode ? 'dark-mode' : ''}`}
            onClick={toggleEconomicHotspot}
            title={t?.economicHotspot || "经济地理热点"}
            aria-label={t?.economicHotspot || "经济地理热点"}
          >
            <span className="map-button-icon"><IoFlameOutline /></span>
          </button>
          {/* 悬停提示框 */}
          <div className={`map-button-tooltip ${isDarkMode ? 'dark-mode' : ''}`}>
            {t?.economicHotspot || "经济地理热点"}
          </div>
        </div>

        {/* 设置按钮 */}
        <MapPopover
          trigger={<FiSettings />}
          position="left"
          isDarkMode={isDarkMode}
          tooltipText={t?.settings || "设置"}
        >
          {/* 账户管理 */}
          <button className="map-popover-item">
            <span>{t?.accountManagement || "账户管理"}</span>
          </button>

          {/* 单位设置 */}
          <div className="settings-expandable">
            <button
              className="map-popover-item"
              onClick={() => setUnitsExpanded(!unitsExpanded)}
            >
              <div className="popover-item-content">
                <span>{t?.units || "单位设置"}</span>
                <motion.span
                  className="expand-icon"
                  initial={false}
                  animate={{ rotate: unitsExpanded ? 90 : 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  ▶
                </motion.span>
              </div>
            </button>

            <AnimatePresence>
              {unitsExpanded && (
                <motion.div
                  className="settings-expanded-content"
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <button
                    className={`map-popover-subitem ${selectedUnit === 'metric' ? 'active' : ''}`}
                    onClick={() => setSelectedUnit('metric')}
                  >
                    <span>{t?.metric || "公制 (米, 平方米)"}</span>
                  </button>
                  <button
                    className={`map-popover-subitem ${selectedUnit === 'imperial' ? 'active' : ''}`}
                    onClick={() => setSelectedUnit('imperial')}
                  >
                    <span>{t?.imperial || "英制 (英尺, 平方英尺)"}</span>
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 货币设置 */}
          <div className="settings-expandable">
            <button
              className="map-popover-item"
              onClick={() => setCurrencyExpanded(!currencyExpanded)}
            >
              <div className="popover-item-content">
                <span>{t?.currency || "货币设置"}</span>
                <motion.span
                  className="expand-icon"
                  initial={false}
                  animate={{ rotate: currencyExpanded ? 90 : 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  ▶
                </motion.span>
              </div>
            </button>

            <AnimatePresence>
              {currencyExpanded && (
                <motion.div
                  className="settings-expanded-content"
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                >
                  <button
                    className={`map-popover-subitem ${selectedCurrency === 'USD' ? 'active' : ''}`}
                    onClick={() => setSelectedCurrency('USD')}
                  >
                    <span>USD ($)</span>
                  </button>
                  <button
                    className={`map-popover-subitem ${selectedCurrency === 'THB' ? 'active' : ''}`}
                    onClick={() => setSelectedCurrency('THB')}
                  >
                    <span>THB (฿)</span>
                  </button>
                  <button
                    className={`map-popover-subitem ${selectedCurrency === 'CNY' ? 'active' : ''}`}
                    onClick={() => setSelectedCurrency('CNY')}
                  >
                    <span>CNY (¥)</span>
                  </button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 我的数据 */}
          <button className="map-popover-item">
            <span>{t?.myData || "我的数据"}</span>
          </button>

          {/* 关于我们 */}
          <button className="map-popover-item">
            <span>{t?.aboutUs || "关于我们"}</span>
          </button>
        </MapPopover>
      </div>

      {/* 右侧按钮组：过滤器和搜索 */}
      <div className={`map-buttons-container map-buttons-right ${isDarkMode ? 'dark-mode' : ''}`}>
        {/* 过滤器按钮 */}
        <button
          ref={filterButtonRef}
          className={`map-button pill with-text ${showFilters ? 'active' : ''} ${isDarkMode ? 'dark-mode' : ''}`}
          onClick={() => {
            setShowFilters(!showFilters);
            if (!showFilters) setShowSearch(false); // 打开过滤器时关闭搜索
          }}
          title={t?.toggleFilters || "切换过滤器"}
          aria-label={t?.toggleFilters || "切换过滤器"}
        >
          <span className="map-button-icon"><IoOptionsOutline /></span>
          <span className="map-button-text">{t?.filters || "过滤器"}</span>
          {activeFiltersCount > 0 && (
            <span className="map-button-badge" aria-label={`${activeFiltersCount} active filters`}></span>
          )}
        </button>

        {/* 搜索按钮 */}
        <button
          ref={searchButtonRef}
          className={`map-button pill with-text ${showSearch ? 'active' : ''} ${isDarkMode ? 'dark-mode' : ''}`}
          onClick={() => {
            setShowSearch(!showSearch);
            if (!showSearch) setShowFilters(false); // 打开搜索时关闭过滤器
          }}
          title={t?.search || "搜索"}
          aria-label={t?.search || "搜索"}
          style={{ marginTop: '10px' }}
        >
          <span className="map-button-icon"><FiSearch /></span>
          <span className="map-button-text">{t?.search || "搜索"}</span>
        </button>
      </div>

      {/* 过滤器面板（动画） - 从按钮位置优雅展开 */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            className={`map-filter-panel ${isDarkMode ? 'dark-mode' : ''}`}
            initial={{
              opacity: 0,
              scale: 0.95,
              y: -10,
              transformOrigin: 'top right',
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
              transformOrigin: 'top right',
            }}
            exit={{
              opacity: 0,
              scale: 0.95,
              y: -10,
              transformOrigin: 'top right',
            }}
            transition={{ 
              type: 'spring', 
              stiffness: 500, 
              damping: 30,
              mass: 0.5
            }}
          >
            <div className="map-filter-panel-arrow"></div>
            <div className="map-filter-header">
              <h3>{t?.filters || "过滤器"}</h3>
              <button
                className={`map-button circle ${isDarkMode ? 'dark-mode' : ''}`}
                onClick={() => setShowFilters(false)}
              >
                <span className="map-button-icon"><FiX /></span>
              </button>
            </div>

            <div className="map-filter-content">
              {/* 美国过滤器 */}
              {selectedCountry === 'USA' && (
                <>
                  {/* 租赁成本 */}
                  <div className="map-filter-section">
                    <label className="map-filter-label">{t?.leaseCostRange || "租赁成本范围"} ($)</label>
                    <div className="lease-cost-inputs">
                      <input
                        type="text"
                        inputMode="numeric"
                        placeholder={t?.min || "最小值"}
                        value={minLease}
                        onChange={(e) => handleLeaseInputChange(e.target.value, 'min')}
                        className="filter-input"
                      />
                      <span>-</span>
                      <input
                        type="text"
                        inputMode="numeric"
                        placeholder={t?.max || "最大值"}
                        value={maxLease}
                        onChange={(e) => handleLeaseInputChange(e.target.value, 'max')}
                        className="filter-input"
                      />
                    </div>
                  </div>

                  {/* 州选择器 */}
                  <div className="map-filter-section">
                    <div className="filter-section-header">
                      <label className="map-filter-label">{t?.states || "州"}</label>
                      <div className="filter-actions">
                        <button onClick={handleSelectAllStates}>{t?.all || "全部"}</button>
                        <button onClick={handleClearStates}>{t?.clear || "清除"}</button>
                      </div>
                    </div>
                    {/* 州列表 */}
                    <div className="multi-select-list" style={{ maxHeight: stateListExpanded ? '150px' : '80px' }}>
                      {stateOptions.map(state => (
                        <label className="filter-checkbox-label" key={state}>
                          <input
                            type="checkbox"
                            checked={selectedStates.includes(state)}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              setSelectedStates(prev =>
                                isChecked ? [...prev, state] : prev.filter(s => s !== state)
                              );
                            }}
                          />
                          <span className="custom-checkbox">
                            {selectedStates.includes(state) ? <FiCheckSquare /> : <FiSquare />}
                          </span>
                          {stateFullNames[state]} ({state})
                        </label>
                      ))}
                    </div>
                    {/* 展开/折叠按钮 */}
                    {stateOptions.length > 5 && (
                      <button
                        className="expand-collapse-button"
                        onClick={() => setStateListExpanded(!stateListExpanded)}
                      >
                        {stateListExpanded ? <FiMinimize2 /> : <FiMaximize2 />}
                        <span>{stateListExpanded ? (t?.showLess || '显示更少') : (t?.showMore || '显示更多')}</span>
                      </button>
                    )}
                  </div>

                  {/* 城市选择器 */}
                  <div className="map-filter-section">
                    <div className="filter-section-header">
                      <label className="map-filter-label">{t?.cities || "城市"}</label>
                      <div className="filter-actions">
                        <button onClick={() => setSelectedCities(cityOptions)}>{t?.all || "全部"}</button>
                        <button onClick={() => setSelectedCities([])}>{t?.clear || "清除"}</button>
                      </div>
                    </div>
                    <input
                      type="text"
                      placeholder={t?.searchCities || "搜索城市..."}
                      className="filter-input search-input"
                      value={citySearchTerm}
                      onChange={(e) => setCitySearchTerm(e.target.value)}
                    />
                    <div className="multi-select-list" style={{ maxHeight: cityListExpanded ? '150px' : '80px' }}>
                      {filteredCityOptions.length > 0 ? filteredCityOptions.map(city => (
                        <label className="filter-checkbox-label" key={city}>
                          <input
                            type="checkbox"
                            checked={selectedCities.includes(city)}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              setSelectedCities(prev =>
                                isChecked ? [...prev, city] : prev.filter(c => c !== city)
                              );
                            }}
                          />
                          <span className="custom-checkbox">
                            {selectedCities.includes(city) ? <FiCheckSquare /> : <FiSquare />}
                          </span>
                          {city}
                        </label>
                      )) : <div className="no-results-text">{t?.noCitiesFound || "没有找到匹配的城市。"}</div>}
                    </div>
                    {/* 展开/折叠按钮 */}
                    {filteredCityOptions.length > 5 && (
                      <button
                        className="expand-collapse-button"
                        onClick={() => setCityListExpanded(!cityListExpanded)}
                      >
                        {cityListExpanded ? <FiMinimize2 /> : <FiMaximize2 />}
                        <span>{cityListExpanded ? (t?.showLess || '显示更少') : (t?.showMore || '显示更多')}</span>
                      </button>
                    )}
                  </div>
                </>
              )}

              {/* 泰国过滤器 */}
              {selectedCountry === 'THAILAND' && (
                <div className="map-filter-section">
                  <label className="map-filter-label">{t?.thaiProvinces || "省份"}</label>
                  <div className="province-button-grid">
                    {Object.keys(THAILAND_PROVINCE_COORDINATES)
                      .filter(p => p !== 'DEFAULT')
                      .map(province => (
                        <button
                          key={province}
                          className="province-button"
                          onClick={() => handleFocusThaiProvince(province)}
                          title={`${t?.zoomTo || "缩放到"} ${THAILAND_PROVINCE_COORDINATES[province].name}`}
                        >
                          {THAILAND_PROVINCE_COORDINATES[province].name}
                        </button>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 搜索面板（动画） - 从按钮位置优雅展开 */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            className={`map-filter-panel map-search-panel ${isDarkMode ? 'dark-mode' : ''}`}
            initial={{
              opacity: 0,
              scale: 0.95,
              y: -10,
              transformOrigin: 'top right',
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
              transformOrigin: 'top right',
            }}
            exit={{
              opacity: 0,
              scale: 0.95,
              y: -10,
              transformOrigin: 'top right',
            }}
            transition={{ 
              type: 'spring', 
              stiffness: 500, 
              damping: 30,
              mass: 0.5
            }}
          >
            <div className="map-search-panel-arrow"></div>
            <div className="map-filter-header">
              <h3>{t?.search || "搜索"}</h3>
              <button
                className={`map-button circle ${isDarkMode ? 'dark-mode' : ''}`}
                onClick={() => setShowSearch(false)}
              >
                <span className="map-button-icon"><FiX /></span>
              </button>
            </div>

            <div className="map-filter-content">
              <div className="map-filter-section">
                <label className="map-filter-label">{t?.searchLocations || "搜索位置"}</label>
                <div className="search-input-container">
                  <input
                    type="text"
                    className="filter-input search-input"
                    placeholder={t?.enterLocationName || "输入位置名称..."}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && searchTerm.trim()) {
                        performSearch();
                      }
                    }}
                  />
                  <button
                    className="search-button"
                    onClick={() => {
                      if (searchTerm.trim()) {
                        performSearch();
                      }
                    }}
                    disabled={isSearching || !searchTerm.trim()}
                  >
                    {isSearching ? '...' : <FiSearch />}
                  </button>
                </div>
              </div>

              {/* 搜索结果区域 */}
              <div className="map-filter-section">
                <label className="map-filter-label">{t?.results || "搜索结果"}</label>
                <div className="search-results">
                  {!hasSearched ? (
                    <div className="no-results-text">
                      {t?.enterSearchTerm || "输入搜索词查找位置"}
                    </div>
                  ) : isSearching ? (
                    <div className="no-results-text">
                      {t?.searching || "搜索中..."}
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div className="search-results-list">
                      {searchResults.map((result, index) => {
                        const name = selectedCountry === 'USA' ? result.park : result.name;
                        const location = selectedCountry === 'USA'
                          ? `${result.city}, ${result.state}`
                          : THAILAND_PROVINCE_COORDINATES[result.province]?.name || 'Thailand';

                        // 检查是否是通过州名搜索找到的结果
                        const isStateMatch = selectedCountry === 'USA' &&
                          searchTerm.trim().length > 0 &&
                          (result.state.toLowerCase() === searchTerm.toLowerCase().trim() ||
                           (stateFullNames[result.state] &&
                            stateFullNames[result.state].toLowerCase().includes(searchTerm.toLowerCase().trim())));

                        // 检查是否是通过名称搜索找到的结果
                        const isNameMatch = searchTerm.trim().length > 0 &&
                          name.toLowerCase().includes(searchTerm.toLowerCase().trim());

                        return (
                          <div
                            key={index}
                            className="search-result-item"
                            onClick={() => handleSearchResultClick(result)}
                          >
                            <div className="search-result-name">{name}</div>
                            <div className="search-result-location">
                              {location}
                            </div>
                            <div className="search-result-tags">
                              {isStateMatch && (
                                <span className="match-indicator state-match-indicator">
                                  {t?.stateMatch || "州匹配"}
                                </span>
                              )}
                              {isNameMatch && (
                                <span className="match-indicator name-match-indicator">
                                  {t?.nameMatch || "名称匹配"}
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="no-results-text">
                      {t?.noResultsFound || `没有找到匹配"${searchTerm}"的园区`}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedMapControls;
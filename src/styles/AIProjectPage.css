/* AI Project Page Styles - Modern Design */
.ai-project-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  color: #fff;
}

/* 全局强制深色主题 - 防止任何白色背景 */
.ai-project-page *,
.ai-project-page *::before,
.ai-project-page *::after {
  box-sizing: border-box;
}

.ai-project-page .analysis-results *,
.ai-project-page .results-list *,
.ai-project-page .result-category *,
.ai-project-page .result-items *,
.ai-project-page .result-tag-container *,
.ai-project-page [contenteditable] {
  background-color: transparent !important;
  color: inherit !important;
}

.ai-project-page [contenteditable]:focus {
  background-color: rgba(77, 200, 255, 0.12) !important;
  color: #4dc8ff !important;
}

/* 简化优化背景效果 */
.ai-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  /* 修改为更深色的背景，提高对比度 */
  background: linear-gradient(135deg, #050a12 0%, #071525 100%);
}

/* 将其他背景元素暂时隐藏，只保留技术电路 */
.grid-pattern,
.data-flow-lines,
.glowing-nodes,
.geometric-shapes,
.ambient-gradients {
  display: none;
}

/* 强化的技术电路背景 - 完全重新设计 */
.tech-circuit {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用更亮的颜色和更强的对比度 */
  background-image: 
    /* 水平线 */
    linear-gradient(90deg, transparent 96%, rgba(0, 255, 255, 0.5) 96%),
    /* 垂直线 */
    linear-gradient(0deg, transparent 96%, rgba(0, 255, 255, 0.5) 96%);
  background-size: 60px 60px;
  opacity: 1;
  z-index: 0;
}

/* 添加发光节点 - 完全重新设计 */
.tech-circuit::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    /* 在交叉点添加较大的发光点 */
    radial-gradient(circle at 60px 60px, rgba(0, 255, 255, 0.8) 2px, transparent 2px);
  background-size: 60px 60px;
  z-index: 1;
  animation: nodeGlow 4s infinite alternate;
}

/* 添加流动连接线 - 完全重新设计 */
.tech-circuit::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    /* 添加一些随机的斜线 */
    linear-gradient(45deg, transparent 40%, rgba(0, 255, 255, 0.6) 40%, rgba(0, 255, 255, 0.6) 60%, transparent 60%),
    linear-gradient(135deg, transparent 40%, rgba(0, 255, 255, 0.6) 40%, rgba(0, 255, 255, 0.6) 60%, transparent 60%);
  background-size: 180px 180px;
  background-position: 0 0;
  z-index: 2;
  opacity: 0.5;
  animation: circuitFlow 10s linear infinite;
  /* 添加模糊效果增强辉光 */
  filter: blur(0.5px);
}

/* 添加随机光点效果 */
.ai-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.8) 1px, transparent 1px),
    radial-gradient(circle at 40% 70%, rgba(0, 255, 255, 0.8) 1px, transparent 1px),
    radial-gradient(circle at 65% 20%, rgba(0, 255, 255, 0.8) 1px, transparent 1px),
    radial-gradient(circle at 80% 50%, rgba(0, 255, 255, 0.8) 1px, transparent 1px);
  z-index: 3;
  filter: blur(1px);
  animation: lightBlink 3s infinite alternate;
}

/* 光点闪烁动画 */
@keyframes lightBlink {
  0% { opacity: 0.3; }
  50% { opacity: 0.8; }
  100% { opacity: 0.3; }
}

/* 节点发光动画 */
@keyframes nodeGlow {
  0% { opacity: 0.5; filter: blur(0px); }
  50% { opacity: 1; filter: blur(1px); }
  100% { opacity: 0.5; filter: blur(0px); }
}

/* 电路流动动画 */
@keyframes circuitFlow {
  0% { background-position: 0 0; }
  100% { background-position: 180px 180px; }
}

/* 几何形状 */
.geometric-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 15% 15%, rgba(25, 118, 210, 0.1) 0%, transparent 5%),
    radial-gradient(circle at 85% 85%, rgba(0, 212, 255, 0.1) 0%, transparent 5%),
    radial-gradient(circle at 85% 15%, rgba(64, 180, 229, 0.1) 0%, transparent 5%),
    radial-gradient(circle at 15% 85%, rgba(25, 118, 210, 0.1) 0%, transparent 5%);
  opacity: 0.6;
}

/* 环境渐变 */
.ambient-gradients {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    linear-gradient(225deg, rgba(25, 118, 210, 0.05) 0%, transparent 50%);
  animation: shift-gradient 20s ease infinite alternate;
}

/* 渐变移动动画 */
@keyframes shift-gradient {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

/* 确保输入和结果面板顶部对齐 */
.workspace {
  display: flex;
  gap: 20px;
  align-items: flex-start; /* 让两个容器顶部对齐 */
}

.modern-section {
  flex: 1;
  margin-top: 0; /* 确保没有上边距 */
}

.modern-card {
  height: 100%;
  margin-top: 0;
  display: flex;
  flex-direction: column;
}

/* 移动设备响应式调整 */
@media (max-width: 768px) {
  .workspace {
    flex-direction: column;
  }
}

/* 现代化顶部导航 */
.ai-header {
  position: relative;
  z-index: 10;
  padding: 24px 0;
  background: rgba(10, 15, 28, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(77, 200, 255, 0.15);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

/* 现代化返回按钮 */
.back-button.modern-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.9);
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.back-button.modern-btn:hover {
  background: rgba(77, 200, 255, 0.15);
  border-color: rgba(77, 200, 255, 0.3);
  transform: translateX(-3px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.2);
}

.back-button.modern-btn .btn-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.back-button.modern-btn:hover .btn-icon {
  transform: translateX(-2px);
}

/* 项目信息区域 */
.project-info {
  flex: 1;
  text-align: center;
  max-width: 600px;
}

.project-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.project-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
}

.project-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  background: rgba(77, 200, 255, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(77, 200, 255, 0.2);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #4dc8ff;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.active {
  background: #00ff88;
  box-shadow: 0 0 8px rgba(0, 255, 136, 0.4);
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.project-date {
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
}

/* 现代化语言下拉菜单 */
.language-dropdown.modern-dropdown {
  position: relative;
  flex-shrink: 0;
}

.language-dropdown-trigger.modern-trigger {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  min-width: 140px;
  z-index: 100;
}

.language-dropdown-trigger.modern-trigger:hover {
  background: rgba(77, 200, 255, 0.12);
  border-color: rgba(77, 200, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.15);
}

.current-language {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.current-language .language-flag {
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.current-language .language-name {
  font-weight: 500;
}

.dropdown-arrow {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(255, 255, 255, 0.7);
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.dropdown-arrow svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.language-dropdown-menu.modern-menu {
  position: absolute;
  top: calc(100% + 12px);
  right: 0;
  background: rgba(10, 15, 28, 0.95);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(25px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 16px rgba(77, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1000;
  min-width: 220px;
  overflow: hidden;
  animation: modernDropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modernDropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-12px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.08) 100%);
  color: #4dc8ff;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(77, 200, 255, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-header .header-icon {
  font-size: 14px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.language-option.modern-option {
  display: flex;
  align-items: center;
  gap: 14px;
  width: 100%;
  padding: 14px 20px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  position: relative;
}

.language-option.modern-option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.language-option.modern-option:hover {
  background: rgba(77, 200, 255, 0.08);
  color: #fff;
  transform: translateX(2px);
}

.language-option.modern-option:hover::before {
  transform: scaleY(1);
}

.language-option.modern-option.active {
  background: rgba(77, 200, 255, 0.15);
  color: #4dc8ff;
}

.language-option.modern-option.active::before {
  transform: scaleY(1);
}

.language-flag {
  font-size: 18px;
  width: 24px;
  text-align: center;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.language-name {
  flex: 1;
  font-weight: 500;
}

.check-mark {
  color: #4dc8ff;
  font-weight: bold;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.check-mark svg {
  filter: drop-shadow(0 1px 2px rgba(77, 200, 255, 0.4));
}

/* 现代化主内容 */
.ai-main {
  position: relative;
  z-index: 5;
  padding: 40px 0 60px 0;
}

/* 优化的视图控制按钮 */
.view-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
  animation: fadeInUp 0.6s ease-out;
  position: relative;
}

.view-controls::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(77, 200, 255, 0.2) 50%, transparent 100%);
  transform: translate(-50%, -50%);
  z-index: 0;
}

.view-toggle-btn {
  background: rgba(10, 15, 28, 0.8);
  border: 1px solid rgba(77, 200, 255, 0.2);
  color: rgba(77, 200, 255, 0.8);
  padding: 14px 28px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(77, 200, 255, 0.1);
  position: relative;
  z-index: 1;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.view-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.05) 0%, rgba(77, 200, 255, 0.1) 100%);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.view-toggle-btn:hover {
  background: rgba(10, 15, 28, 0.9);
  border-color: rgba(77, 200, 255, 0.4);
  color: #4dc8ff;
  transform: translateY(-3px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(77, 200, 255, 0.2);
}

.view-toggle-btn:hover::before {
  opacity: 1;
}

.view-toggle-btn.active {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  color: #4dc8ff;
  box-shadow:
    0 8px 32px rgba(77, 200, 255, 0.2),
    0 4px 16px rgba(77, 200, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.view-toggle-btn.active:hover {
  transform: translateY(-3px);
  box-shadow:
    0 12px 40px rgba(77, 200, 255, 0.25),
    0 6px 20px rgba(77, 200, 255, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.workspace.modern-workspace {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  min-height: calc(100vh - 220px);
  align-items: start;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.workspace.modern-workspace.results-only {
  grid-template-columns: 1fr;
  max-width: 800px;
  margin: 0 auto;
}

.workspace.modern-workspace.results-only .input-section.hidden {
  opacity: 0;
  transform: translateX(-100%);
  pointer-events: none;
  position: absolute;
  width: 0;
  overflow: hidden;
}

.workspace.modern-workspace.results-only .results-section {
  animation: expandToCenter 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes expandToCenter {
  from {
    transform: translateX(50%);
    opacity: 0.8;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 现代化区域样式 */
.modern-section {
  display: flex;
  flex-direction: column;
  height: fit-content;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-section.hidden {
  opacity: 0;
  transform: translateX(-50px);
  pointer-events: none;
}

/* 现代化卡片设计 */
.modern-card {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(77, 200, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(77, 200, 255, 0.3) 50%, transparent 100%);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    0 4px 16px rgba(77, 200, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(77, 200, 255, 0.15);
}

/* 卡片头部 */
.card-header {
  padding: 24px 28px 20px 28px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.2) 0%, rgba(77, 200, 255, 0.1) 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4dc8ff;
  border: 1px solid rgba(77, 200, 255, 0.2);
  flex-shrink: 0;
}

.header-icon svg {
  filter: drop-shadow(0 2px 4px rgba(77, 200, 255, 0.3));
}

.header-text h2 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 6px 0;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

/* 输入统计信息 */
.input-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.char-count {
  padding: 4px 8px;
  background: rgba(77, 200, 255, 0.1);
  border-radius: 6px;
  font-weight: 500;
}

/* 输入容器 */
.input-container {
  padding: 28px 28px 20px 28px;
}

.input-textarea.modern-textarea {
  width: 100%;
  min-height: 320px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 20px;
  color: #fff;
  font-size: 14px;
  line-height: 1.7;
  resize: vertical;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
  font-weight: 400;
}

.input-textarea.modern-textarea:focus {
  outline: none;
  border-color: rgba(77, 200, 255, 0.4);
  box-shadow:
    0 0 0 3px rgba(77, 200, 255, 0.1),
    0 4px 12px rgba(77, 200, 255, 0.05);
  background: rgba(255, 255, 255, 0.05);
}

.input-textarea.modern-textarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

/* 快捷输入芯片区域 - 移到输入框外面 */
.suggestion-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 12px 28px 0 28px;
  margin-bottom: 8px;
}

.suggestion-chip {
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.suggestion-chip:hover {
  background: rgba(77, 200, 255, 0.15);
  border-color: rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.15);
}

/* 现代化操作按钮区域 */
.input-actions.modern-actions {
  display: flex;
  gap: 16px;
  padding: 0 28px 28px 28px;
  align-items: center;
}

/* 科技感分析按钮 */
.analyze-btn.modern-primary-btn {
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 50%, #4dc8ff 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #0a0f1c;
  padding: 18px 36px;
  border-radius: 16px;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 14px;
  box-shadow:
    0 8px 32px rgba(77, 200, 255, 0.3),
    0 4px 16px rgba(77, 200, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  flex: 1;
  justify-content: center;
  min-height: 56px;
  letter-spacing: 0.8px;
  text-transform: uppercase;
  backdrop-filter: blur(10px);
}

.analyze-btn.modern-primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transition: left 0.6s ease;
}

.analyze-btn.modern-primary-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
}

.analyze-btn.modern-primary-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 48px rgba(77, 200, 255, 0.4),
    0 6px 24px rgba(77, 200, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, #00d4ff 0%, #4dc8ff 50%, #00d4ff 100%);
}

.analyze-btn.modern-primary-btn:hover:not(:disabled)::before {
  left: 100%;
}

.analyze-btn.modern-primary-btn:hover:not(:disabled)::after {
  width: 100px;
  height: 100px;
}

.analyze-btn.modern-primary-btn:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  box-shadow:
    0 6px 24px rgba(77, 200, 255, 0.3),
    0 3px 12px rgba(77, 200, 255, 0.2);
}

.analyze-btn.modern-primary-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.1);
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.3) 0%, rgba(77, 200, 255, 0.2) 100%);
}

.analyze-btn.modern-primary-btn .btn-icon {
  transition: all 0.4s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.analyze-btn.modern-primary-btn:hover:not(:disabled) .btn-icon {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

/* 现代化次要按钮 */
.clear-btn.modern-secondary-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.8);
  padding: 14px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  min-height: 48px;
}

.clear-btn.modern-secondary-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.clear-btn.modern-secondary-btn .btn-icon {
  transition: transform 0.3s ease;
}

.clear-btn.modern-secondary-btn:hover .btn-icon {
  transform: scale(1.1);
}

/* 科技感加载动画 */
.loading-spinner.modern-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(10, 15, 28, 0.2);
  border-top: 2px solid #0a0f1c;
  border-right: 2px solid rgba(10, 15, 28, 0.4);
  border-radius: 50%;
  animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.loading-spinner.modern-spinner::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 50%;
  animation: modernSpinReverse 2s linear infinite;
}

@keyframes modernSpin {
  0% {
    transform: rotate(0deg);
    border-top-color: #0a0f1c;
  }
  25% {
    border-top-color: rgba(77, 200, 255, 0.8);
  }
  50% {
    border-top-color: #0a0f1c;
  }
  75% {
    border-top-color: rgba(77, 200, 255, 0.6);
  }
  100% {
    transform: rotate(360deg);
    border-top-color: #0a0f1c;
  }
}

@keyframes modernSpinReverse {
  0% { transform: rotate(0deg); opacity: 0.3; }
  50% { opacity: 0.8; }
  100% { transform: rotate(-360deg); opacity: 0.3; }
}

/* 结果统计信息 */
.results-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.results-count {
  padding: 4px 8px;
  background: rgba(0, 255, 136, 0.1);
  border-radius: 6px;
  font-weight: 500;
  color: #00ff88;
}

/* 现代化空状态 */
.no-results.modern-empty {
  text-align: center;
  padding: 80px 28px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-illustration {
  margin-bottom: 24px;
  color: rgba(77, 200, 255, 0.3);
  animation: emptyPulse 3s ease-in-out infinite;
}

@keyframes emptyPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

.no-results.modern-empty h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 8px 0;
}

.no-results.modern-empty p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  line-height: 1.5;
}

/* 现代化错误消息 */
.error-message.modern-error {
  background: rgba(255, 71, 87, 0.08);
  border: 1px solid rgba(255, 71, 87, 0.2);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin: 28px;
  backdrop-filter: blur(10px);
}

.error-message.modern-error .error-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 71, 87, 0.15);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4757;
  flex-shrink: 0;
}

.error-content h4 {
  color: #ff4757;
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.error-content p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.retry-btn.modern-retry-btn {
  background: rgba(255, 71, 87, 0.15);
  border: 1px solid rgba(255, 71, 87, 0.3);
  color: #ff4757;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 6px;
}

.retry-btn.modern-retry-btn:hover {
  background: rgba(255, 71, 87, 0.25);
  border-color: rgba(255, 71, 87, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.2);
}

/* 现代化分析结果 - 修复白色背景问题 */
.analysis-results.modern-results {
  padding: 28px;
  background: transparent;
  color: inherit;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  background: transparent;
}

.results-header h3 {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  background: transparent;
}

.results-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.export-btn.modern-action-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.7);
  padding: 10px 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  font-size: 13px;
  font-weight: 500;
}

.export-btn.modern-action-btn:hover {
  background: rgba(77, 200, 255, 0.12);
  border-color: rgba(77, 200, 255, 0.25);
  color: #4dc8ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.15);
}

.edit-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.edit-hint .hint-icon {
  font-size: 12px;
  filter: grayscale(0.3);
}

.edit-hint .hint-text {
  font-weight: 400;
}

/* 简化的分析结果列表 - 确保深色主题 */
.results-list.modern-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: transparent;
}

.result-category.simple-category {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  color: inherit;
}

.result-category.simple-category:hover {
  border-color: rgba(77, 200, 255, 0.12);
  background: rgba(255, 255, 255, 0.03);
}

.category-header.enhanced-header {
  padding: 16px 20px 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
  transition: all 0.2s ease;
}

.category-header.enhanced-header:hover {
  background: rgba(77, 200, 255, 0.02);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.category-header.enhanced-header h4 {
  color: #4dc8ff;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.add-item-btn {
  background: rgba(77, 200, 255, 0.08);
  border: 1px solid rgba(77, 200, 255, 0.2);
  color: #4dc8ff;
  padding: 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.add-item-btn:hover {
  background: rgba(77, 200, 255, 0.15);
  border-color: rgba(77, 200, 255, 0.3);
  opacity: 1;
  transform: scale(1.05);
}

.item-count {
  background: rgba(77, 200, 255, 0.12);
  color: #4dc8ff;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

.result-items.simple-items {
  padding: 12px 20px 16px 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  align-items: flex-start;
}

.result-tag-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(77, 200, 255, 0.06);
  border: 1px solid rgba(77, 200, 255, 0.15);
  border-radius: 14px;
  padding: 2px;
  transition: all 0.2s ease;
  animation: tagFadeIn 0.3s ease-out;
}

.result-tag-container:hover {
  background: rgba(77, 200, 255, 0.1);
  border-color: rgba(77, 200, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.1);
}

.result-tag.enhanced-tag {
  background: transparent !important;
  border: none;
  color: #4dc8ff !important;
  padding: 6px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: text;
  outline: none;
  min-width: 20px;
  text-align: center;
  /* 强制覆盖任何可能的白色背景 */
  -webkit-text-fill-color: #4dc8ff !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
}

.result-tag.enhanced-tag:hover {
  background: rgba(77, 200, 255, 0.08) !important;
  color: #4dc8ff !important;
}

.result-tag.enhanced-tag:focus {
  background: rgba(77, 200, 255, 0.12) !important;
  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);
  color: #4dc8ff !important;
}

.delete-item-btn {
  background: rgba(255, 71, 87, 0.1);
  border: 1px solid rgba(255, 71, 87, 0.2);
  color: rgba(255, 71, 87, 0.8);
  padding: 4px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  width: 20px;
  height: 20px;
}

.result-tag-container:hover .delete-item-btn {
  opacity: 1;
  transform: scale(1);
}

.delete-item-btn:hover {
  background: rgba(255, 71, 87, 0.2);
  border-color: rgba(255, 71, 87, 0.4);
  color: #ff4757;
  transform: scale(1.1);
}

@keyframes tagFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.no-items.simple-no-items {
  color: rgba(255, 255, 255, 0.3);
  font-style: italic;
  font-size: 12px;
  padding: 8px 0;
  text-align: center;
}

.error-container {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  color: #fff;
}

.error-container h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #ff4757;
}

/* 现代化响应式设计 */
@media (max-width: 1200px) {
  .workspace.modern-workspace {
    gap: 24px;
  }

  .workspace.modern-workspace.results-only {
    max-width: 700px;
  }

  .results-list.modern-list {
    gap: 10px;
  }

  .view-controls {
    margin-bottom: 20px;
  }

  .view-toggle-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 1024px) {
  .workspace.modern-workspace {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .workspace.modern-workspace.results-only {
    max-width: 600px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .project-info {
    order: 1;
  }

  .back-button.modern-btn {
    order: 0;
    align-self: flex-start;
  }

  .language-dropdown.modern-dropdown {
    order: 2;
    align-self: flex-end;
  }

  .project-title {
    font-size: 1.6rem;
  }

  .language-dropdown-menu.modern-menu {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  .results-list.modern-list {
    gap: 8px;
  }

  .view-toggle-btn {
    padding: 8px 16px;
    font-size: 12px;
  }

  .result-tag-container {
    margin-bottom: 4px;
  }

  .edit-hint {
    display: none;
  }
}

@media (max-width: 768px) {
  .ai-container {
    padding: 0 16px;
  }

  .ai-header {
    padding: 20px 0;
  }

  .header-content {
    padding: 0 16px;
    gap: 12px;
  }

  .back-button.modern-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .project-title {
    font-size: 1.4rem;
  }

  .language-dropdown-trigger.modern-trigger {
    padding: 10px 14px;
    font-size: 13px;
    min-width: 120px;
  }

  .language-dropdown-menu.modern-menu {
    min-width: 200px;
  }

  .modern-card {
    border-radius: 16px;
  }

  .card-header {
    padding: 20px 20px 16px 20px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
  }

  .header-text h2 {
    font-size: 1.2rem;
  }

  .input-container {
    padding: 20px;
  }

  .input-textarea.modern-textarea {
    min-height: 280px;
    padding: 16px;
  }

  .suggestion-chips {
    gap: 6px;
  }

  .suggestion-chip {
    font-size: 10px;
    padding: 5px 10px;
  }

  .input-actions.modern-actions {
    flex-direction: column;
    padding: 0 20px 20px 20px;
    gap: 12px;
  }

  .analyze-btn.modern-primary-btn,
  .clear-btn.modern-secondary-btn {
    width: 100%;
    justify-content: center;
  }

  .analysis-results.modern-results {
    padding: 20px;
  }

  .results-list.modern-list {
    gap: 8px;
  }

  .category-header.enhanced-header {
    padding: 14px 16px 10px 16px;
  }

  .result-items.simple-items {
    padding: 10px 16px 14px 16px;
    gap: 6px;
  }

  .view-controls {
    margin-bottom: 16px;
  }

  .view-toggle-btn {
    padding: 8px 14px;
    font-size: 11px;
  }

  .add-item-btn {
    padding: 4px;
  }

  .result-tag-container {
    gap: 2px;
  }

  .result-tag.enhanced-tag {
    padding: 4px 8px;
    font-size: 11px;
  }

  .delete-item-btn {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .ai-container {
    padding: 0 12px;
  }

  .header-content {
    padding: 0 12px;
  }

  .project-title {
    font-size: 1.2rem;
  }

  .workspace.modern-workspace {
    gap: 16px;
  }

  .card-header {
    padding: 16px 16px 12px 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
  }

  .header-icon {
    width: 36px;
    height: 36px;
  }

  .header-text h2 {
    font-size: 1.1rem;
  }

  .input-container {
    padding: 16px;
  }

  .input-textarea.modern-textarea {
    min-height: 240px;
    padding: 14px;
    font-size: 13px;
  }

  .input-actions.modern-actions {
    padding: 0 16px 16px 16px;
  }

  .analysis-results.modern-results {
    padding: 16px;
  }

  .view-controls {
    margin-bottom: 12px;
  }

  .view-toggle-btn {
    padding: 6px 12px;
    font-size: 10px;
    gap: 6px;
  }

  .workspace.modern-workspace.results-only {
    max-width: 100%;
  }

  .result-tag-container {
    padding: 1px;
  }

  .result-tag.enhanced-tag {
    padding: 3px 6px;
    font-size: 10px;
  }

  .delete-item-btn {
    width: 16px;
    height: 16px;
  }

  .add-item-btn {
    padding: 3px;
  }

  .results-actions {
    gap: 8px;
  }

  .export-btn.modern-action-btn {
    padding: 6px 8px;
  }
}

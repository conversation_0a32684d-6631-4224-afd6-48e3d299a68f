@echo off
chcp 65001 >nul

echo 🚀 Starting AI Site Analyzer...
echo 正在启动AI选址分析器...

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    echo ❌ 未安装Node.js，请先安装Node.js。
    pause
    exit /b 1
)

REM 检查npm是否安装
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed. Please install npm first.
    echo ❌ 未安装npm，请先安装npm。
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
echo 正在安装依赖...

REM 安装前端依赖
if not exist "node_modules" (
    echo Installing frontend dependencies...
    call npm install
)

REM 准备后端依赖
if not exist "server-node_modules\package.json" (
    echo Setting up backend dependencies...
    if not exist "server-node_modules" mkdir server-node_modules
    copy server-package.json server-node_modules\package.json >nul
    cd server-node_modules
    call npm install
    cd ..
)

echo 🌐 Starting servers...
echo 正在启动服务器...

REM 启动后端服务器
echo Starting backend API server on port 3001...
cd server-node_modules
start /b node ..\server.js
cd ..

REM 等待后端服务器启动
timeout /t 3 /nobreak >nul

REM 启动前端开发服务器
echo Starting frontend development server...
echo 正在启动前端开发服务器...

echo.
echo 🎉 AI Site Analyzer is starting up!
echo 🎉 AI选址分析器正在启动！
echo.
echo 📱 Frontend: http://localhost:5174
echo 🔧 Backend API: http://localhost:3001
echo.
echo Press Ctrl+C to stop servers
echo 按 Ctrl+C 停止服务器

call npm run dev

#!/bin/bash

# AI Site Analyzer 启动脚本
# Start script for AI Site Analyzer

echo "🚀 Starting AI Site Analyzer..."
echo "正在启动AI选址分析器..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "❌ 未安装Node.js，请先安装Node.js。"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    echo "❌ 未安装npm，请先安装npm。"
    exit 1
fi

echo "📦 Installing dependencies..."
echo "正在安装依赖..."

# 安装前端依赖
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

# 准备后端依赖
if [ ! -f "server-node_modules/package.json" ]; then
    echo "Setting up backend dependencies..."
    mkdir -p server-node_modules
    cp server-package.json server-node_modules/package.json
    cd server-node_modules
    npm install
    cd ..
fi

echo "🌐 Starting servers..."
echo "正在启动服务器..."

# 启动后端服务器（在后台）
echo "Starting backend API server on port 3001..."
cd server-node_modules
node ../server.js &
BACKEND_PID=$!
cd ..

# 等待后端服务器启动
sleep 3

# 检查后端服务器是否启动成功
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Backend API server started successfully"
    echo "✅ 后端API服务器启动成功"
else
    echo "⚠️  Backend API server may not be running, will use fallback mode"
    echo "⚠️  后端API服务器可能未运行，将使用回退模式"
fi

# 启动前端开发服务器
echo "Starting frontend development server..."
echo "正在启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 AI Site Analyzer is starting up!"
echo "🎉 AI选址分析器正在启动！"
echo ""
echo "📱 Frontend: http://localhost:5174"
echo "🔧 Backend API: http://localhost:3001"
echo ""
echo "Press Ctrl+C to stop all servers"
echo "按 Ctrl+C 停止所有服务器"

# 等待用户中断
trap "echo ''; echo 'Stopping servers...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT

# 保持脚本运行
wait
